#!/bin/bash

# 大文件操作演示运行脚本
# 适用于JDK8环境

echo "=== 大文件操作演示启动脚本 ==="
echo "JDK版本: $(java -version 2>&1 | head -n 1)"
echo "当前目录: $(pwd)"
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保Java已正确安装并配置PATH"
    exit 1
fi

# 检查JDK版本
JAVA_VERSION=$(java -version 2>&1 | grep -oP 'version "\K[^"]+' | cut -d'.' -f1-2)
echo "检测到Java版本: $JAVA_VERSION"

# 设置JVM参数
JVM_OPTS="-Xmx4g -XX:MaxDirectMemorySize=2g -XX:+UseG1GC"
echo "JVM参数: $JVM_OPTS"
echo

# 编译所有Java文件
echo "=== 编译Java文件 ==="
find src/main/java -name "*.java" -print0 | xargs -0 javac -d target/classes
if [ $? -ne 0 ]; then
    echo "编译失败，请检查代码"
    exit 1
fi
echo "编译完成"
echo

# 检查测试文件是否存在
TEST_FILE="src/main/resources/large-file-1gb.dat"
if [ ! -f "$TEST_FILE" ]; then
    echo "=== 生成1GB测试文件 ==="
    echo "警告: 这将需要几分钟时间和1GB磁盘空间"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        java $JVM_OPTS -cp target/classes com.ray.demo.LargeFileTestGenerator
        if [ $? -ne 0 ]; then
            echo "测试文件生成失败"
            exit 1
        fi
    else
        echo "取消执行"
        exit 0
    fi
else
    echo "测试文件已存在: $TEST_FILE"
    echo "文件大小: $(du -h "$TEST_FILE" | cut -f1)"
fi
echo

# 运行演示程序
echo "=== 选择要运行的演示 ==="
echo "1. BIO大文件操作演示"
echo "2. MappedByteBuffer演示"  
echo "3. 综合性能对比测试"
echo "4. 运行所有演示"
echo "5. 退出"
echo

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "运行BIO大文件操作演示..."
        java $JVM_OPTS -cp target/classes com.ray.demo.bio.BIOLargeFileOperations
        ;;
    2)
        echo "运行MappedByteBuffer演示..."
        java $JVM_OPTS -cp target/classes com.ray.demo.nio.MappedByteBufferDemo
        ;;
    3)
        echo "运行综合性能对比测试..."
        java $JVM_OPTS -cp target/classes com.ray.demo.LargeFilePerformanceComparison
        ;;
    4)
        echo "运行所有演示..."
        echo
        echo "--- BIO大文件操作演示 ---"
        java $JVM_OPTS -cp target/classes com.ray.demo.bio.BIOLargeFileOperations
        echo
        echo "--- MappedByteBuffer演示 ---"
        java $JVM_OPTS -cp target/classes com.ray.demo.nio.MappedByteBufferDemo
        echo
        echo "--- 综合性能对比测试 ---"
        java $JVM_OPTS -cp target/classes com.ray.demo.LargeFilePerformanceComparison
        ;;
    5)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo
echo "=== 演示完成 ==="
echo "查看输出目录中的详细报告:"
echo "  - target/bio-large-output/"
echo "  - target/nio-mapped-output/"  
echo "  - target/performance-comparison/"
